package com.official.invoicegenarator;

import android.Manifest;
import android.app.DownloadManager;
import android.content.Context;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Bundle;
import android.os.Environment;
import android.text.Editable;
import android.text.TextWatcher;
import android.util.Log;
import android.view.View;
import android.widget.EditText;
import android.widget.ProgressBar;
import android.widget.RadioGroup;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.official.invoicegenarator.adapters.FileListAdapter;
import com.official.invoicegenarator.network.ApiService;
import com.official.invoicegenarator.network.ApiCallback;
import com.official.invoicegenarator.models.FileInfo;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

/**
 * DownloadListActivity - File listing and download functionality
 * Replaces Firebase Storage implementation with custom API backend
 */
public class DownloadListActivity extends AppCompatActivity implements FileListAdapter.OnFileClickListener {

    private static final String TAG = "DownloadListActivity";
    private static final int PERMISSION_REQUEST_CODE = 1001;

    private ApiService apiService;
    private RecyclerView recyclerView;
    private FileListAdapter adapter;
    private ProgressBar loadingProgressBar;
    private EditText searchEditText;
    private RadioGroup sortOptions;

    private List<FileInfo> allFiles = new ArrayList<>();
    private List<FileInfo> filteredFiles = new ArrayList<>();

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_download_list);

        initializeViews();
        setupRecyclerView();
        setupSearchAndSort();

        // Initialize API service
        apiService = new ApiService();

        // Check permissions and load files
        if (checkPermissions()) {
            loadFiles();
        } else {
            requestPermissions();
        }
    }

    private void initializeViews() {
        recyclerView = findViewById(R.id.recyclerView);
        loadingProgressBar = findViewById(R.id.loadingProgressBar);
        searchEditText = findViewById(R.id.searchEditText);
        sortOptions = findViewById(R.id.sortOptions);
    }

    private void setupRecyclerView() {
        adapter = new FileListAdapter(this, filteredFiles, this);
        recyclerView.setLayoutManager(new LinearLayoutManager(this));
        recyclerView.setAdapter(adapter);
    }

    private void setupSearchAndSort() {
        // Setup search functionality
        searchEditText.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {}

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                filterFiles(s.toString());
            }

            @Override
            public void afterTextChanged(Editable s) {}
        });

        // Setup sort functionality
        sortOptions.setOnCheckedChangeListener((group, checkedId) -> {
            if (checkedId == R.id.sortNewest) {
                sortFiles(true); // newest first
            } else if (checkedId == R.id.sortOldest) {
                sortFiles(false); // oldest first
            }
        });
    }

    private boolean checkPermissions() {
        return ContextCompat.checkSelfPermission(this, Manifest.permission.WRITE_EXTERNAL_STORAGE)
                == PackageManager.PERMISSION_GRANTED;
    }

    private void requestPermissions() {
        ActivityCompat.requestPermissions(this,
                new String[]{Manifest.permission.WRITE_EXTERNAL_STORAGE},
                PERMISSION_REQUEST_CODE);
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == PERMISSION_REQUEST_CODE) {
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                loadFiles();
            } else {
                Toast.makeText(this, "Storage permission is required to download files", Toast.LENGTH_LONG).show();
                finish();
            }
        }
    }

    private void loadFiles() {
        showLoading(true);

        apiService.listFiles(new ApiCallback<List<FileInfo>>() {
            @Override
            public void onSuccess(List<FileInfo> files) {
                runOnUiThread(() -> {
                    showLoading(false);
                    allFiles.clear();
                    allFiles.addAll(files);
                    filterFiles(searchEditText.getText().toString());

                    if (files.isEmpty()) {
                        Toast.makeText(DownloadListActivity.this, "No files found", Toast.LENGTH_SHORT).show();
                    }
                });
            }

            @Override
            public void onError(String error) {
                runOnUiThread(() -> {
                    showLoading(false);
                    Log.e(TAG, "Failed to load files: " + error);
                    Toast.makeText(DownloadListActivity.this, "Failed to load files: " + error, Toast.LENGTH_LONG).show();
                });
            }
        });
    }

    private void filterFiles(String query) {
        filteredFiles.clear();

        if (query.isEmpty()) {
            filteredFiles.addAll(allFiles);
        } else {
            String lowerQuery = query.toLowerCase();
            for (FileInfo file : allFiles) {
                if (file.getOriginalName().toLowerCase().contains(lowerQuery)) {
                    filteredFiles.add(file);
                }
            }
        }

        // Apply current sort
        int checkedId = sortOptions.getCheckedRadioButtonId();
        if (checkedId == R.id.sortNewest) {
            sortFiles(true);
        } else if (checkedId == R.id.sortOldest) {
            sortFiles(false);
        }

        adapter.notifyDataSetChanged();
    }

    private void sortFiles(boolean newestFirst) {
        Collections.sort(filteredFiles, new Comparator<FileInfo>() {
            @Override
            public int compare(FileInfo f1, FileInfo f2) {
                if (newestFirst) {
                    return f2.getCreatedAt().compareTo(f1.getCreatedAt());
                } else {
                    return f1.getCreatedAt().compareTo(f2.getCreatedAt());
                }
            }
        });
        adapter.notifyDataSetChanged();
    }

    private void showLoading(boolean show) {
        loadingProgressBar.setVisibility(show ? View.VISIBLE : View.GONE);
        recyclerView.setVisibility(show ? View.GONE : View.VISIBLE);
    }

    // FileListAdapter.OnFileClickListener implementation
    @Override
    public void onFileClick(FileInfo file) {
        downloadFile(file);
    }

    @Override
    public void onFileDelete(FileInfo file) {
        deleteFile(file);
    }

    private void downloadFile(FileInfo file) {
        if (!checkPermissions()) {
            requestPermissions();
            return;
        }

        Toast.makeText(this, "Downloading " + file.getOriginalName(), Toast.LENGTH_SHORT).show();

        apiService.downloadFile(file.getName(), new ApiCallback<byte[]>() {
            @Override
            public void onSuccess(byte[] data) {
                runOnUiThread(() -> {
                    try {
                        saveFileToDownloads(file.getOriginalName(), data);
                        Toast.makeText(DownloadListActivity.this,
                            "Downloaded: " + file.getOriginalName(), Toast.LENGTH_SHORT).show();
                    } catch (IOException e) {
                        Log.e(TAG, "Failed to save file", e);
                        Toast.makeText(DownloadListActivity.this,
                            "Failed to save file: " + e.getMessage(), Toast.LENGTH_LONG).show();
                    }
                });
            }

            @Override
            public void onError(String error) {
                runOnUiThread(() -> {
                    Log.e(TAG, "Download failed: " + error);
                    Toast.makeText(DownloadListActivity.this,
                        "Download failed: " + error, Toast.LENGTH_LONG).show();
                });
            }
        });
    }

    private void deleteFile(FileInfo file) {
        // Show confirmation dialog before deleting
        new androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("Delete File")
            .setMessage("Are you sure you want to delete " + file.getOriginalName() + "?")
            .setPositiveButton("Delete", (dialog, which) -> {
                performDelete(file);
            })
            .setNegativeButton("Cancel", null)
            .show();
    }

    private void performDelete(FileInfo file) {
        apiService.deleteFile(file.getName(), new ApiCallback<Void>() {
            @Override
            public void onSuccess(Void result) {
                runOnUiThread(() -> {
                    Toast.makeText(DownloadListActivity.this,
                        "Deleted: " + file.getOriginalName(), Toast.LENGTH_SHORT).show();

                    // Remove from lists and refresh
                    allFiles.remove(file);
                    filteredFiles.remove(file);
                    adapter.notifyDataSetChanged();
                });
            }

            @Override
            public void onError(String error) {
                runOnUiThread(() -> {
                    Log.e(TAG, "Delete failed: " + error);
                    Toast.makeText(DownloadListActivity.this,
                        "Delete failed: " + error, Toast.LENGTH_LONG).show();
                });
            }
        });
    }

    private void saveFileToDownloads(String filename, byte[] data) throws IOException {
        File downloadsDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS);
        File file = new File(downloadsDir, filename);

        try (FileOutputStream fos = new FileOutputStream(file)) {
            fos.write(data);
            fos.flush();
        }

        // Notify media scanner about the new file
        DownloadManager downloadManager = (DownloadManager) getSystemService(Context.DOWNLOAD_SERVICE);
        if (downloadManager != null) {
            // This will make the file visible in Downloads app
            downloadManager.addCompletedDownload(
                filename,
                "Downloaded from MtcInvoice",
                true,
                getMimeType(filename),
                file.getAbsolutePath(),
                data.length,
                true
            );
        }
    }

    private String getMimeType(String filename) {
        String extension = filename.substring(filename.lastIndexOf('.') + 1).toLowerCase();
        switch (extension) {
            case "pdf":
                return "application/pdf";
            case "jpg":
            case "jpeg":
                return "image/jpeg";
            case "png":
                return "image/png";
            case "doc":
                return "application/msword";
            case "docx":
                return "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
            default:
                return "application/octet-stream";
        }
    }
}
